# Database Security Configuration

This directory contains sensitive configuration files that should be properly secured in a production environment.

## Security Recommendations

1. **Environment Variables**: In production, store sensitive information in environment variables instead of configuration files.

2. **File Permissions**: Set restrictive permissions on sensitive files:
   ```
   chmod 0600 .keys
   chmod 0600 security.php
   chmod 0600 config.php
   ```

3. **Directory Location**: In production, move sensitive configuration files outside the web root directory.

4. **Encryption Keys**: Generate new encryption keys and IVs for production:
   ```php
   // Generate in PHP
   $new_key = bin2hex(random_bytes(32));
   $new_iv = bin2hex(random_bytes(8));
   ```

5. **SSL/TLS**: Enable SSL/TLS for database connections if your database server supports it.

6. **Regular Updates**: Regularly update your encryption keys and security practices.

## Setting Up Environment Variables

### For Apache:

Add to your virtual host configuration or .htaccess file:
```
SetEnv DB_HOST "your_host"
SetEnv DB_USER "your_username"
SetEnv DB_PASSWORD "your_password"
SetEnv DB_NAME "your_database"
SetEnv DB_ENCRYPTION_KEY "your_encryption_key"
SetEnv DB_ENCRYPTION_IV "your_iv"
```

### For Nginx with PHP-FPM:

Add to your PHP-FPM pool configuration:
```
env[DB_HOST] = "your_host"
env[DB_USER] = "your_username"
env[DB_PASSWORD] = "your_password"
env[DB_NAME] = "your_database"
env[DB_ENCRYPTION_KEY] = "your_encryption_key"
env[DB_ENCRYPTION_IV] = "your_iv"
```

## Security Checklist

- [ ] Generated new encryption keys for production
- [ ] Moved sensitive files outside web root
- [ ] Set proper file permissions
- [ ] Configured environment variables
- [ ] Enabled SSL/TLS for database connection
- [ ] Implemented proper error handling
- [ ] Tested configuration in development environment
