<?php
/**
 * Security Configuration
 * 
 * This file contains security-related constants and configurations.
 * In production, these values should be stored in environment variables
 * or a secure configuration system outside the web root.
 * 
 * WARNING: This file contains sensitive information and should be protected
 * with proper file permissions (0600 or similar) and kept outside web-accessible directories.
 */

// Database encryption settings
// IMPORTANT: Change these values in production!
define('DB_ENCRYPTION_KEY', 'f2c8e7b5ad23d4f901b2e3c4a8f7d6c7e5b4f3a1c2d8e9f0a4b6c8d7e5f3a2c1');
define('DB_ENCRYPTION_IV', '1234567890123456'); // 16 bytes

// Security headers (for reference)
define('SECURITY_HEADERS', [
    'X-Content-Type-Options' => 'nosniff',
    'X-Frame-Options' => 'SAMEORIGIN',
    'X-XSS-Protection' => '1; mode=block',
    'Strict-Transport-Security' => 'max-age=31536000; includeSubDomains',
    'Content-Security-Policy' => "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'",
    'Referrer-Policy' => 'strict-origin-when-cross-origin',
    'Permissions-Policy' => 'geolocation=(), microphone=(), camera=()'
]);

// CSRF token settings
define('CSRF_TOKEN_EXPIRY', 3600); // 1 hour in seconds

/**
 * Function to apply security headers
 */
function apply_security_headers() {
    foreach (SECURITY_HEADERS as $header => $value) {
        header("$header: $value");
    }
}

/**
 * Generate a secure random string
 * 
 * @param int $length Length of the random string
 * @return string Secure random string
 */
function generate_secure_string($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

/**
 * Generate a new encryption key
 * 
 * @return string New encryption key
 */
function generate_encryption_key() {
    return bin2hex(random_bytes(32));
}

/**
 * Generate a new initialization vector
 * 
 * @return string New initialization vector
 */
function generate_iv() {
    return random_bytes(16);
}

/**
 * Encrypt data using AES-256-CBC
 * 
 * @param string $data Data to encrypt
 * @param string $key Encryption key (optional, uses DB_ENCRYPTION_KEY if not provided)
 * @param string $iv Initialization vector (optional, uses DB_ENCRYPTION_IV if not provided)
 * @return string|false Encrypted data or false on failure
 */
function secure_encrypt($data, $key = null, $iv = null) {
    $key = $key ?? DB_ENCRYPTION_KEY;
    $iv = $iv ?? DB_ENCRYPTION_IV;
    
    return openssl_encrypt($data, 'aes-256-cbc', $key, 0, $iv);
}

/**
 * Decrypt data using AES-256-CBC
 * 
 * @param string $data Data to decrypt
 * @param string $key Encryption key (optional, uses DB_ENCRYPTION_KEY if not provided)
 * @param string $iv Initialization vector (optional, uses DB_ENCRYPTION_IV if not provided)
 * @return string|false Decrypted data or false on failure
 */
function secure_decrypt($data, $key = null, $iv = null) {
    $key = $key ?? DB_ENCRYPTION_KEY;
    $iv = $iv ?? DB_ENCRYPTION_IV;
    
    return openssl_decrypt($data, 'aes-256-cbc', $key, 0, $iv);
}
