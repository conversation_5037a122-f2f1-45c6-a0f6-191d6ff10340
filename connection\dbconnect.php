<?php
require_once __DIR__ . "/../config/env.php";

$host = env("DB_HOST", "localhost");
$port = env("DB_PORT", "3306");
$dbname = env("DB_NAME", "prod_calaguas");
$username = env("DB_USER", "root");
$password = env("DB_PASSWORD", "");

try {
    $dsn = "mysql:host=$host;port=$port;dbname=$dbname;charset=utf8mb4";
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
        PDO::ATTR_TIMEOUT => 5,
        PDO::MYSQL_ATTR_FOUND_ROWS => true,
        PDO::ATTR_PERSISTENT => false,
    ];
    
    $pdo = new PDO($dsn, $username, $password, $options);
    unset($host, $port, $username, $password, $dsn);
    
} catch (PDOException $e) {
    error_log("Database connection failed: " . $e->getMessage());
    if (env("APP_DEBUG", false)) {
        die("Database connection failed: " . $e->getMessage());
    } else {
        die("Database connection failed. Please try again later.");
    }
}
?>