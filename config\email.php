<?php
require_once __DIR__ . "/env.php";

define("EMAIL_FROM_ADDRESS", env("EMAIL_FROM_ADDRESS", "<EMAIL>"));
define("EMAIL_FROM_NAME", env("EMAIL_FROM_NAME", "Calaguas Booking System"));

function sendPasswordResetEmail($to, $username, $resetLink) {
    $subject = "Password Reset Request - " . EMAIL_FROM_NAME;
    $message = getPasswordResetEmailTemplate($username, $resetLink);
    $headers = getEmailHeaders();
    return mail($to, $subject, $message, $headers);
}

function sendPasswordResetConfirmationEmail($to, $username) {
    $subject = "Password Reset Successful - " . EMAIL_FROM_NAME;
    $message = getPasswordResetConfirmationTemplate($username);
    $headers = getEmailHeaders();
    return mail($to, $subject, $message, $headers);
}

function getEmailHeaders() {
    $headers = "MIME-Version: 1.0\r\n";
    $headers .= "Content-type:text/html;charset=UTF-8\r\n";
    $headers .= "From: " . EMAIL_FROM_NAME . " <" . EMAIL_FROM_ADDRESS . ">\r\n";
    $headers .= "Reply-To: " . EMAIL_FROM_ADDRESS . "\r\n";
    return $headers;
}

function getPasswordResetEmailTemplate($username, $resetLink) {
    return "
    <html>
    <body style=\"font-family: Arial, sans-serif; padding: 20px;\">
        <h2 style=\"color: #3b82f6;\">Password Reset Request</h2>
        <p>Hello <strong>" . htmlspecialchars($username) . "</strong>,</p>
        <p>Click the link below to reset your password:</p>
        <p><a href=\"" . $resetLink . "\" style=\"background: #3b82f6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;\">Reset Password</a></p>
        <p>This link expires in 1 hour.</p>
        <p>If you did not request this, please ignore this email.</p>
    </body>
    </html>";
}

function getPasswordResetConfirmationTemplate($username) {
    return "
    <html>
    <body style=\"font-family: Arial, sans-serif; padding: 20px;\">
        <h2 style=\"color: #10b981;\">Password Reset Successful</h2>
        <p>Hello <strong>" . htmlspecialchars($username) . "</strong>,</p>
        <p>Your password has been successfully reset.</p>
        <p>You can now log in with your new password.</p>
    </body>
    </html>";
}

function logEmailAttempt($type, $to, $success, $error = "") {
    global $pdo;
    try {
        $description = "Email: $type to $to";
        if (!$success) $description .= " - FAILED";
        if (strlen($description) > 100) $description = substr($description, 0, 97) . "...";
        
        $stmt = $pdo->prepare("INSERT INTO system_logs (type, description) VALUES (?, ?)");
        $stmt->execute(["Email", $description]);
    } catch (Exception $e) {
        error_log("Failed to log email: " . $e->getMessage());
    }
}
?>