<?php
/**
 * Database Configuration
 *
 * This file contains encrypted database credentials.
 * The actual encryption keys are stored separately in a more secure location.
 */

// Include security functions if not already included
if (!function_exists('secure_encrypt')) {
    require_once __DIR__ . '/security.php';
}

// Get encryption details from a more secure location
$keyFile = __DIR__ . '/.keys';
if (file_exists($keyFile)) {
    $keys = parse_ini_file($keyFile);
    $encryptionKey = $keys['encryption_key'] ?? null;
    $ivString = $keys['iv'] ?? null;
} else {
    // Fallback to environment variables
    $encryptionKey = getenv('DB_ENCRYPTION_KEY');
    $ivString = getenv('DB_ENCRYPTION_IV');

    // Last resort fallback (not recommended for production)
    if (!$encryptionKey || !$ivString) {
        $encryptionKey = defined('DB_ENCRYPTION_KEY') ? DB_ENCRYPTION_KEY : null;
        $ivString = defined('DB_ENCRYPTION_IV') ? DB_ENCRYPTION_IV : null;
    }
}

// Function to encrypt database credentials
function encrypt_db_value($value, $key, $iv) {
    if (function_exists('secure_encrypt')) {
        return secure_encrypt($value, $key, $iv);
    }
    return openssl_encrypt($value, 'aes-256-cbc', $key, 0, $iv);
}

// Get database credentials from environment variables if available
$dbHost = getenv('DB_HOST') ?: 'localhost';
$dbUser = getenv('DB_USER') ?: 'root';
$dbPassword = getenv('DB_PASSWORD') ?: '';
$dbName = getenv('DB_NAME') ?: 'prod_calaguas';

// Return encrypted configuration
return [
    'DB_HOST' => encrypt_db_value($dbHost, $encryptionKey, $ivString),
    'DB_USER' => encrypt_db_value($dbUser, $encryptionKey, $ivString),
    'DB_PASSWORD' => encrypt_db_value($dbPassword, $encryptionKey, $ivString),
    'DB_NAME' => encrypt_db_value($dbName, $encryptionKey, $ivString),

    // Additional configuration options
    'DB_PORT' => getenv('DB_PORT') ?: '3306',
    'DB_CHARSET' => 'utf8mb4',
    'DB_COLLATION' => 'utf8mb4_unicode_ci',

    // Application settings
    'APP_ENV' => getenv('APP_ENV') ?: 'development',
    'DEBUG' => getenv('APP_DEBUG') === 'true',
];
